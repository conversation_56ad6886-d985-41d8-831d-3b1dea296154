import { useEffect } from 'react'
import { StytchLogin, useStytchSession } from '@stytch/react'
import { OAuthProviders, Products } from '@stytch/vanilla-js'
import { useNavigate } from 'react-router'
import { Box, Card, CardContent, Typography, Chip } from '@mui/material'
import { CheckCircle } from '@mui/icons-material'

import { stytchHelper } from './helpers'
import { URL_PATH_DASHBOARD } from 'constants'

const AuthLogin = () => {
  const navigate = useNavigate()
  const { session } = useStytchSession()

  useEffect(() => {
    if (session) {
      navigate(URL_PATH_DASHBOARD)
    }
  }, [navigate, session])

  const styles = {
    container: {
      width: '450px'
    },
    buttons: {
      primary: {
        backgroundColor: '#038666',
        borderColor: '#038666'
      }
    }
  }

  const redirectUrl = `${window.location.origin}${stytchHelper.loginSignupRedirectUrl}`

  const config = {
    products: [Products.emailMagicLinks, Products.oauth, Products.passwords],
    emailMagicLinksOptions: {
      loginRedirectURL: redirectUrl,
      loginExpirationMinutes: stytchHelper.sessionDurationMinutes,
      signupRedirectURL: redirectUrl,
      signupExpirationMinutes: stytchHelper.sessionDurationMinutes
    },
    oauthOptions: {
      providers: [{ type: OAuthProviders.Google }],
      loginRedirectURL: redirectUrl,
      signupRedirectURL: redirectUrl
    }
  }

  return (
    <Box
      sx={{
        display: 'flex',
        gap: 3,
        alignItems: 'flex-start',
        flexDirection: { xs: 'column', md: 'row' },
        maxWidth: '900px',
        margin: '0 auto',
        padding: 2
      }}
    >
      {/* Login Component */}
      <Box sx={{ flex: 1 }}>
        <StytchLogin config={config} styles={styles} />
      </Box>

      {/* Information Card */}
      <Card
        sx={{
          flex: 1,
          minWidth: { xs: '100%', md: '300px' },
          backgroundColor: 'white',
          borderRadius: '16px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
          border: '1px solid #e0e0e0'
        }}
      >
        <CardContent sx={{ p: 3 }}>
          <Typography
            variant="h6"
            sx={{
              mb: 3,
              fontWeight: 600,
              color: '#038666',
              textAlign: 'center'
            }}
          >
            Get Started Today
          </Typography>

          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
              <CheckCircle sx={{ color: '#038666', fontSize: 20 }} />
              <Typography variant="body1" sx={{ color: '#333' }}>
                No credit cards required
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
              <CheckCircle sx={{ color: '#038666', fontSize: 20 }} />
              <Typography variant="body1" sx={{ color: '#333' }}>
                Free 7 days trial
              </Typography>
            </Box>
          </Box>

          <Box sx={{ mt: 3, textAlign: 'center' }}>
            <Chip
              label="Start Free Trial"
              sx={{
                backgroundColor: '#038666',
                color: 'white',
                fontWeight: 500,
                px: 2,
                '&:hover': {
                  backgroundColor: '#026b54'
                }
              }}
            />
          </Box>
        </CardContent>
      </Card>
    </Box>
  )
}

export default AuthLogin
