import { useEffect } from 'react'
import { StytchLogin, useStytchSession } from '@stytch/react'
import { OAuthProviders, Products } from '@stytch/vanilla-js'
import { useNavigate } from 'react-router'
import { Box } from '@mui/material'

import { stytchHelper } from './helpers'
import { URL_PATH_DASHBOARD } from 'constants'
import AuthTrialInfoCard from './auth-trial-info-card'

const AuthLogin = () => {
  const navigate = useNavigate()
  const { session } = useStytchSession()

  useEffect(() => {
    if (session) {
      navigate(URL_PATH_DASHBOARD)
    }
  }, [navigate, session])

  const styles = {
    container: {
      width: '450px'
    },
    buttons: {
      primary: {
        backgroundColor: '#038666',
        borderColor: '#038666'
      }
    }
  }

  const redirectUrl = `${window.location.origin}${stytchHelper.loginSignupRedirectUrl}`

  const config = {
    products: [Products.emailMagicLinks, Products.oauth, Products.passwords],
    emailMagicLinksOptions: {
      loginRedirectURL: redirectUrl,
      loginExpirationMinutes: stytchHelper.sessionDurationMinutes,
      signupRedirectURL: redirectUrl,
      signupExpirationMinutes: stytchHelper.sessionDurationMinutes
    },
    oauthOptions: {
      providers: [{ type: OAuthProviders.Google }],
      loginRedirectURL: redirectUrl,
      signupRedirectURL: redirectUrl
    }
  }

  return (
    <Box
      sx={{
        display: 'flex',
        gap: 2,
        alignItems: 'flex-start',
        flexDirection: { xs: 'column', md: 'row' },
        maxWidth: '900px',
        margin: '0 auto',
        padding: 2
      }}
    >
      {/* Information Card - Left Side */}
      <Box sx={{ flex: 1, minWidth: { xs: '100%', md: '300px' } }}>
        <AuthTrialInfoCard />
      </Box>

      {/* Login Component - Right Side */}
      <Box sx={{ flex: 1 }}>
        <StytchLogin config={config} styles={styles} />
      </Box>
    </Box>
  )
}

export default AuthLogin
