import { Box, Card, CardContent, Typography, Chip } from '@mui/material'
import { CheckCircle } from '@mui/icons-material'
import LoginImg from 'assets/images/login.png'
import Logo from 'assets/images/bi.svg'

const AuthTrialInfoCard = () => {
  return (
    <Card 
      sx={{ 
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
        border: '1px solid #e0e0e0',
        height: 'fit-content',
        maxWidth: '448px'
      }}
    >
      <CardContent sx={{ p: 3 }}>
        <Box sx={{textAlign: 'center'}}>
          <img src={Logo} height={46}/>
          <img src={LoginImg} height={185}/>
        </Box>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
            <CheckCircle sx={{ color: '#038666', fontSize: 20 }} />
            <Typography variant="body1" sx={{ color: '#333' }}>
              No credit cards required
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
            <CheckCircle sx={{ color: '#038666', fontSize: 20 }} />
            <Typography variant="body1" sx={{ color: '#333' }}>
              Free 7 days trial
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
            <CheckCircle sx={{ color: '#038666', fontSize: 20 }} />
            <Typography variant="body1" sx={{ color: '#333' }}>
              Automatically accept the offers
            </Typography>
          </Box>
        </Box>
      </CardContent>
    </Card>
  )
}

export default AuthTrialInfoCard
