import React from 'react'
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Box,
  <PERSON>ltip,
  IconButton
} from '@mui/material'
import { styled } from '@mui/material/styles'
import InfoIcon from '@mui/icons-material/Info'
import CheckCircleIcon from '@mui/icons-material/CheckCircle'
import TrendingUpIcon from '@mui/icons-material/TrendingUp'

// Styled components for modern design
const StyledCard = styled(Card)(({ theme, isCurrentPlan, isPopular }) => ({
  position: 'relative',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  backgroundColor: 'white',
  borderRadius: 20,
  border: isCurrentPlan
    ? '3px solid #4caf50'
    : isPopular
      ? '3px solid #ff9800'
      : '1px solid #e0e0e0',
  boxShadow: isCurrentPlan
    ? '0 8px 32px rgba(76, 175, 80, 0.2)'
    : isPopular
      ? '0 8px 32px rgba(255, 152, 0, 0.2)'
      : '0 4px 20px rgba(0,0,0,0.08)',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  overflow: 'hidden',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: isCurrentPlan
      ? '0 12px 40px rgba(76, 175, 80, 0.3)'
      : isPopular
        ? '0 12px 40px rgba(255, 152, 0, 0.3)'
        : '0 8px 32px rgba(0,0,0,0.15)'
  }
}))

const PopularBadge = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 24,
  right: -45,
  backgroundColor: '#ff9800',
  color: 'white',
  padding: '6px 36px',
  fontSize: '0.7rem',
  fontWeight: 700,
  textTransform: 'uppercase',
  transform: 'rotate(45deg)',
  zIndex: 2,
  boxShadow: '0 2px 8px rgba(255, 152, 0, 0.3)',
  letterSpacing: '0.5px'
}))

const PriceContainer = styled(Box)(({ theme }) => ({
  background: 'linear-gradient(135deg, #4caf50 0%, #2e7d32 100%)',
  color: 'white',
  padding: `${theme.spacing(2)} ${theme.spacing(2)}`,
  textAlign: 'center',
  position: 'relative',
  [theme.breakpoints.up('sm')]: {
    padding: theme.spacing(2.5)
  },
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 20,
    background: 'white',
    borderRadius: '20px 20px 0 0'
  }
}))

const ActionButton = styled(Button)(({ theme, isCurrentPlan, isPopular }) => ({
  borderRadius: 12,
  padding: theme.spacing(1.5, 4),
  fontSize: '1rem',
  fontWeight: 600,
  textTransform: 'none',
  boxShadow: 'none',
  background: isCurrentPlan
    ? 'linear-gradient(135deg, #4caf50 0%, #45a049 100%)'
    : isPopular
      ? 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)'
      : 'linear-gradient(135deg, #4caf50 0%, #2e7d32 100%)',
  color: 'white',
  '&:hover': {
    boxShadow: isCurrentPlan
      ? '0 4px 16px rgba(76, 175, 80, 0.4)'
      : isPopular
        ? '0 4px 16px rgba(255, 152, 0, 0.4)'
        : '0 4px 16px rgba(76, 175, 80, 0.4)',
    transform: 'translateY(-2px)'
  },
  '&:disabled': {
    background: '#e0e0e0',
    color: '#9e9e9e'
  }
}))

const PlanCard = ({ canAccessCustomerPortal, plan, onSelectPlan, isCurrentPlan = false, onManagePlan }) => {
  const { metadata } = plan
  const { name, items = [], price, priceLabel, description, priceLabel2, is_popular, price_per_day } = metadata

  // Use the is_popular field from metadata
  const isPopular = is_popular === true

  const handleSelectPlan = () => {
    if (!isCurrentPlan) {
      onSelectPlan(plan)
    }
  }

  return (
    <StyledCard isCurrentPlan={isCurrentPlan} isPopular={isPopular && !isCurrentPlan}>
      {/* Badges */}
      {isPopular && !isCurrentPlan && <PopularBadge>Most Popular</PopularBadge>}

      {/* Price Header */}
      <PriceContainer>
        <Typography variant='h5' component='h3' sx={{ fontWeight: 700, mb: 1, color: 'white' }}>
          {name}
        </Typography>

        {description && (
          <Typography variant='body2' sx={{ opacity: 0.95, mb: 2, color: 'white' }}>
            {description}
          </Typography>
        )}

        <Box sx={{ mb: 2 }}>
          {/* Prominent daily price with gold highlighting */}
          {price_per_day ? (
            <>
              <Typography
                variant='h1'
                component='div'
                sx={{
                  fontWeight: 900,
                  display: 'flex',
                  alignItems: 'baseline',
                  justifyContent: 'center',
                  gap: 0.5,
                  position: 'relative',
                  zIndex: 1,
                  color: '#ff9800',
                  textShadow: '0 2px 4px rgba(0,0,0,0.3)',
                  fontSize: '2.5rem'
                }}
              >
                <span style={{ fontSize: '0.8em' }}>$</span>
                {price_per_day} <span style={{ fontSize: '0.5em' }}>/ day</span>
              </Typography>

              {/* Original price as smaller secondary text */}
              {price && (
                <Typography
                  variant='body2'
                  component='div'
                  sx={{
                    fontWeight: 600,
                    display: 'flex',
                    alignItems: 'baseline',
                    justifyContent: 'center',
                    gap: 0.5,
                    mt: 1,
                    opacity: 0.85,
                    color: 'white'
                  }}
                >
                  <span style={{ fontSize: '0.8em' }}>$</span>
                  {price} {priceLabel}
                </Typography>
              )}
            </>
          ) : (
            /* Fallback to original price display for plans without price_per_day */
            <>
              <Typography
                variant='h2'
                component='div'
                sx={{
                  fontWeight: 800,
                  display: 'flex',
                  alignItems: 'baseline',
                  justifyContent: 'center',
                  gap: 0.5,
                  position: 'relative',
                  zIndex: 1
                }}
              >
                <span style={{ fontSize: '0.5em' }}>$</span>
                {price}
              </Typography>

              {priceLabel && (
                <Typography variant='body2' sx={{ opacity: 0.95, mt: 0.5, color: 'white' }}>
                  {priceLabel}
                </Typography>
              )}
            </>
          )}

          {priceLabel2 && (
            <Typography variant='caption' sx={{ opacity: 0.9, display: 'block', mt: 0.5, color: 'white' }}>
              {priceLabel2}
            </Typography>
          )}
        </Box>
      </PriceContainer>

      {/* Content */}
      <CardContent sx={{ p: { xs: 2, sm: 3 }, flexGrow: 1, display: 'flex', flexDirection: 'column' }}>

        {/* Features List */}
        {items.length > 0 && (
          <Box sx={{ py: 0, flexGrow: 1, display: 'flex', flexDirection: 'column', gap: 1 }}>
            {items.map((item, index) => (
              <Box key={index} sx={{ display: 'flex', alignItems: 'flex-start' }}>
                <CheckCircleIcon
                  sx={{
                    color: isCurrentPlan ? '#4caf50' : isPopular ? '#ff9800' : '#4caf50',
                    mr: 2,
                    mt: 0.5,
                    fontSize: 20
                  }}
                />

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flex: 1 }}>
                  <Typography
                    variant='body2'
                    sx={{
                      flex: 1,
                      fontWeight: 500,
                      color: '#1a1a1a',
                      fontSize: '0.95rem'
                    }}
                  >
                    {item.label}
                  </Typography>

                  {item.more && (
                    <Tooltip
                      title={item.more}
                      arrow
                      placement='top'
                      componentsProps={{
                        tooltip: {
                          sx: {
                            backgroundColor: '#1a1a1a',
                            fontSize: '0.875rem',
                            maxWidth: 300
                          }
                        }
                      }}
                    >
                      <IconButton size='small' sx={{ p: 0.5 }}>
                        <InfoIcon fontSize='small' sx={{ color: '#666' }} />
                      </IconButton>
                    </Tooltip>
                  )}
                </Box>
              </Box>
            ))}
          </Box>
        )}

        {/* Action Button */}
        <Box sx={{ mt: 3, textAlign: 'center' }}>
          <ActionButton
            fullWidth
            size='large'
            onClick={canAccessCustomerPortal ? onManagePlan : handleSelectPlan}
            disabled={isCurrentPlan}
            isCurrentPlan={isCurrentPlan}
            isPopular={isPopular}
          >
            {isCurrentPlan
              ? (
                <>
                  <CheckCircleIcon sx={{ mr: 1, fontSize: 20 }} />
                  Current Plan
                </>
                )
              : (
                <>
                  {isPopular && <TrendingUpIcon sx={{ mr: 1, fontSize: 20 }} />}
                  {isPopular ? 'Get Started' : 'Choose Plan'}
                </>
                )}
          </ActionButton>
        </Box>
      </CardContent>
    </StyledCard>
  )
}

export default PlanCard
