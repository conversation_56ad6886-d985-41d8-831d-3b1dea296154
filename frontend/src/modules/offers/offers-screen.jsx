import React, { useState, useEffect } from 'react'
import {
  Container,
  Alert
} from '@mui/material'
import { connect } from 'react-redux'
import API from 'data/api'
import { selectors as offersSelectors } from 'data/offers'
import { getAuthUser } from 'data/auth/selectors'

import OffersSearch from './components/offers-search'
import OffersTabs from './components/offers-tabs'
import OffersList from './components/offers-list'
import OffersPoller from './components/offers-poller'
import OffersSort from './components/offers-sort'
import { OFFER_STATUS } from 'constants/backend'

const OffersScreen = ({
  acceptedOffers,
  missedOffers,
  skippedOffers,
  allOffers,
  fetchOffers,
  fetchOpenMetroOffers,
  stopSearch,
  authUser
}) => {
  const [activeTab, setActiveTab] = useState(OFFER_STATUS.ACCEPTED)
  const [filters, setFilters] = useState({})
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)
  const [isOfferSearchLoading, setIsOfferSearchLoading] = useState(false)
  const [sortBy, setSortBy] = useState('created_at')
  const [sortOrder, setSortOrder] = useState('desc')

  // Load offers on component mount
  useEffect(() => {
    loadOffers()
  }, [])

  // Reload offers when filters change
  useEffect(() => {
    if (Object.keys(filters).length > 0) {
      loadOffers()
    }
  }, [filters])

  const loadOffers = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const queryParams = {
        ...filters,
        session: authUser?.latest_search_session_id
      }

      await fetchOffers(queryParams)
    } catch (err) {
      setError('Failed to load offers. Please try again.')
      console.error('Error loading offers:', err)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSortChange = (newSortBy, newSortOrder) => {
    setSortBy(newSortBy)
    setSortOrder(newSortOrder)
  }

  const handleTabChange = (newTab) => {
    setActiveTab(newTab)
    // Clear status filter when changing tabs to let tab control the status
    const { status, ...otherFilters } = filters
    setFilters(otherFilters)
  }

  const handleSearchStart = async () => {
    setError(null)
    setIsOfferSearchLoading(true)

    try {
      // Call get_open_metro_offers API when starting search
      const response = await fetchOpenMetroOffers()
      // API call successful, now switch to searching state
      setIsOfferSearchLoading(false)
    } catch (err) {
      console.error('Error starting offer search:', err)

      // Extract detailed error information
      let errorMessage = 'Failed to start offer search. Please try again.'

      if (err.response && err.response.data) {
        const errorData = err.response.data
        console.error('Detailed error:', errorData)

        if (errorData.error && errorData.error.details) {
          errorMessage = `Error: ${errorData.error.details}`
        } else if (errorData.error && errorData.error.message) {
          errorMessage = `Error: ${errorData.error.message}`
        } else if (errorData.message) {
          errorMessage = `Error: ${errorData.message}`
        }

        // Show status code if available
        if (errorData.status_code) {
          errorMessage += ` (Status: ${errorData.status_code})`
        }
      } else if (err.message) {
        errorMessage = `Error: ${err.message}`
      }

      setError(errorMessage)
      setIsOfferSearchLoading(false) // Stop API loading state
      // Don't switch to searching state if there's an error
    }
  }

  const handleSearchStop = async () => {
    try {
      // Call the stop search API to update shipt_is_running to false
      await stopSearch()

      setIsOfferSearchLoading(false)
    } catch (err) {
      console.error('Error stopping offer search:', err)

      // Still update local state even if API call fails
      setIsOfferSearchLoading(false)

      // Show error to user
      setError('Failed to stop search. Please try again.')
    }
  }

  const handlePollingError = (error) => {
    console.error('Polling error:', error)
    // Don't stop searching on polling errors, just log them
    // The poller will continue retrying automatically
  }

  // Get filtered and sorted offers based on active tab, filters, and sorting
  const getFilteredOffers = () => {
    let offers = []

    if (activeTab === OFFER_STATUS.ACCEPTED) {
      offers = acceptedOffers
    } else if (activeTab === OFFER_STATUS.GONE) {
      offers = missedOffers
    } else if (activeTab === OFFER_STATUS.SKIPPED) {
      offers = skippedOffers
    }

    // Apply additional filters if any
    if (Object.keys(filters).length > 0) {
      offers = offersSelectors.getFilteredOffers({ entities: { offers: allOffers } }, {
        ...filters,
        status: activeTab === 'accepted' ? 'ac' : 'ms'
      })
    }

    // Apply sorting
    return sortOffers(offers, sortBy, sortOrder)
  }

  // Sort offers based on sortBy and sortOrder
  const sortOffers = (offers, sortBy, sortOrder) => {
    const sortedOffers = [...offers].sort((a, b) => {
      let aValue, bValue

      switch (sortBy) {
        case 'created_at':
          aValue = new Date(a.created_at)
          bValue = new Date(b.created_at)
          break
        case 'estimated_pay':
          aValue = parseFloat(a.offer_data?.tracked_properties?.estimated_pay || 0)
          bValue = parseFloat(b.offer_data?.tracked_properties?.estimated_pay || 0)
          break
        case 'est_drive_time':
          aValue = parseFloat(a.offer_data?.tracked_properties?.est_drive_time || 0)
          bValue = parseFloat(b.offer_data?.tracked_properties?.est_drive_time || 0)
          break
        case 'est_shop_time':
          aValue = parseFloat(a.offer_data?.tracked_properties?.est_shop_time || 0)
          bValue = parseFloat(b.offer_data?.tracked_properties?.est_shop_time || 0)
          break
        default:
          aValue = new Date(a.created_at)
          bValue = new Date(b.created_at)
      }

      if (sortOrder === 'desc') {
        return bValue > aValue ? 1 : bValue < aValue ? -1 : 0
      } else {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
      }
    })

    return sortedOffers
  }

  const filteredOffers = getFilteredOffers()
  const hasFilters = Object.keys(filters).length > 0

  return (
    <Container
      maxWidth='md'
      sx={{
        py: { xs: 2, sm: 3 },
        px: { xs: 2, sm: 3 }
      }}
    >

      {/* Error Alert */}
      {error && (
        <Alert severity='error' sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}
      {/* Search Component */}
      <OffersSearch
        onSearchStart={handleSearchStart}
        onSearchStop={handleSearchStop}
        isSearching={authUser.shipt_is_running}
        isLoading={isOfferSearchLoading}
      />

      {/* Offers Poller - Continuously fetch offers when searching */}
      <OffersPoller
        isSearching={authUser.shipt_is_running}
        onError={handlePollingError}
        params={{ session: authUser?.latest_search_session_id }}
      />

      {/* Filters */}
      {/* <OffersFilter
        onFilterChange={handleFilterChange}
        isLoading={isLoading}
        initialFilters={filters}
      /> */}

      <div>
        {/* Tabs */}
        <OffersTabs
          activeTab={activeTab}
          onTabChange={handleTabChange}
          acceptedCount={acceptedOffers.length}
          missedCount={missedOffers.length}
        />

        {/* Offers Sort */}
        <OffersSort
          sortBy={sortBy}
          sortOrder={sortOrder}
          onSortChange={handleSortChange}
        />
      </div>

      {/* Offers List */}
      <OffersList
        offers={filteredOffers}
        isLoading={isLoading}
        hasFilters={hasFilters}
      />
    </Container>
  )
}

const mapStateToProps = (state) => ({
  acceptedOffers: offersSelectors.getAcceptedOffers(state),
  missedOffers: offersSelectors.getMissedOffers(state),
  skippedOffers: offersSelectors.getSkippedOffers(state),
  allOffers: offersSelectors.getAllOffers(state),
  authUser: getAuthUser(state)
})

const mapDispatchToProps = (dispatch) => ({
  fetchOffers: (params) => dispatch(API.offers.list(params)),
  fetchOpenMetroOffers: () => dispatch(API.shiptbot.start()),
  stopSearch: () => dispatch(API.shiptbot.stop())
})

export default connect(mapStateToProps, mapDispatchToProps)(OffersScreen)
