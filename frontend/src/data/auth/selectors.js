import getOr from 'lodash/fp/getOr'

import { AUTH_STATUS } from 'constants/backend'
import { selectors, ENTITY_TYPES } from 'data/entities/index'

export function getAuthUser (state) {
  return getOr(null, 'auth.user')(state)
}

export function getUserId (state) {
  return getOr(null, 'auth.user.id')(state)
}

export function getAuthStatus (state) {
  return getOr(null, 'auth.authStatus')(state)
}

/**
 * We are in a valid authenticated state. The presence of a token is not enough -
 * we need to be in a state where we have make valid authenticated calls to the backend.
 */
export function isAuthValid (state) {
  return getAuthStatus(state) === AUTH_STATUS.VALID
}

export function getLatestSearchSession (state) {
  return getOr(null, 'auth.user.latest_search_session_id')(state)
}
