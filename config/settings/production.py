# -*- coding: utf-8 -*-
"""
Dev settings
"""
from .common import *  # noqa

# Allowed Hosts
# ------------------------------------------------------------------------------
ALLOWED_HOSTS = env.list("DJANGO_ALLOWED_HOSTS", default=[".onlyshipster.com"])

SECRET_KEY = env("DJANGO_SECRET_KEY")

# CORS Whitelist
# ------------------------------------------------------------------------------
CORS_ALLOWED_ORIGINS = [
    "https://app.onlyshipster.com",
]

# Whitenoise
# ------------------------------------------------------------------------------

SECURITY_MIDDLEWARE = ["django.middleware.security.SecurityMiddleware"]
WHITENOISE_MIDDLEWARE = ["whitenoise.middleware.WhiteNoiseMiddleware"]

MIDDLEWARE = SECURITY_MIDDLEWARE + WHITENOISE_MIDDLEWARE + MIDDLEWARE

STATICFILES_STORAGE = env(
    "DJANGO_STATICFILES_STORAGE",
    default="whitenoise.storage.CompressedManifestStaticFilesStorage",
)

CSRF_TRUSTED_ORIGINS = env.list(
    "DJANGO_CSRF_TRUSTED_ORIGINS",
    default=["https://app.onlyshipster.com", "https://api.onlyshipster.com"],
)
