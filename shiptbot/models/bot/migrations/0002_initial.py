# Generated by Django 5.0.7 on 2025-07-28 02:54

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("bot", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="botsetting",
            name="user",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="day",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="offer",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="range",
            name="day",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="bot.day"
            ),
        ),
        migrations.AddField(
            model_name="range",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="session",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="offer",
            name="session",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="bot.session",
            ),
        ),
        migrations.AddField(
            model_name="store",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="zone",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="store",
            name="zone",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="bot.zone"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="day",
            unique_together={("user", "day")},
        ),
        migrations.AlterUniqueTogether(
            name="range",
            unique_together={("start_time", "end_time", "day", "user")},
        ),
        migrations.AlterUniqueTogether(
            name="store",
            unique_together={("user", "store_id")},
        ),
    ]
