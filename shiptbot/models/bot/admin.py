from django.contrib import admin

from .models import BotSetting, Day, Offer, Range, Store, Zone


class RangeInline(admin.TabularInline):
    model = Range
    extra = 1
    fields = ("start_time", "end_time")
    readonly_fields = ("id",)


@admin.register(BotSetting)
class BotSettingAdmin(admin.ModelAdmin):
    search_fields = ("user__email",)
    raw_id_fields = ("user",)
    readonly_fields = ("id",)
    list_display = (
        "user",
        "refresh_rate_seconds",
    )


@admin.register(Offer)
class OfferAdmin(admin.ModelAdmin):
    search_fields = ("user__email", "session__id")
    readonly_fields = ("id", "created_at")
    raw_id_fields = ("user", "session")
    list_display = (
        "user",
        "status",
        "created_at",
        "skip_reason",
        "session",
    )


@admin.register(Zone)
class ZoneAdmin(admin.ModelAdmin):
    search_fields = (
        "user__email",
        "zone_name",
    )
    readonly_fields = ("id",)
    raw_id_fields = ("user",)
    list_display = (
        "user",
        "zone_name",
    )


@admin.register(Store)
class StoreAdmin(admin.ModelAdmin):
    search_fields = (
        "user__email",
        "store_name",
    )
    readonly_fields = ("id",)
    raw_id_fields = ("user",)
    list_display = (
        "user",
        "store_name",
        "store_id",
        "zone",
        "enabled",
    )


@admin.register(Day)
class DayAdmin(admin.ModelAdmin):
    search_fields = (
        "day",
        "user__email",
    )
    readonly_fields = ("id",)
    list_display = (
        "day",
        "user",
    )
    raw_id_fields = ("user",)
    inlines = [RangeInline]


@admin.register(Range)
class RangeAdmin(admin.ModelAdmin):
    search_fields = ("user__email",)
    readonly_fields = ("id",)
    list_display = (
        "user",
        "day",
        "start_time",
        "end_time",
    )
    raw_id_fields = ("user",)
