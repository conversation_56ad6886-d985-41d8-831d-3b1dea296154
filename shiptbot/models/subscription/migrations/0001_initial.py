# Generated by Django 5.0.7 on 2025-07-28 02:54

import django.db.models.deletion
from django.db import migrations, models

import shiptbot.util.buids


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Plan",
            fields=[
                (
                    "id",
                    shiptbot.util.buids.BUIDField(
                        blank=True,
                        editable=False,
                        max_length=35,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                (
                    "stripe_price_id",
                    models.CharField(blank=True, max_length=50, null=True, unique=True),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Subscription",
            fields=[
                (
                    "id",
                    shiptbot.util.buids.BUIDField(
                        blank=True,
                        editable=False,
                        max_length=35,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("start_date", models.DateTimeField()),
                ("end_date", models.DateTimeField(blank=True, null=True)),
                (
                    "stripe_subscription_id",
                    models.CharField(blank=True, max_length=50, null=True, unique=True),
                ),
                ("stripe_upstream_data", models.JSONField(blank=True, default=dict)),
                ("last_synced_at", models.DateTimeField(auto_now=True)),
                (
                    "plan",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="subscription.plan",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
