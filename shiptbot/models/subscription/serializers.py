from rest_framework import serializers

from .models import Plan, Subscription


class PlanSerializer(serializers.ModelSerializer):
    class Meta:
        model = Plan
        fields = (
            "id",
            "name",
            "metadata",
        )
        read_only_fields = (
            "id",
            "name",
            "metadata",
        )

    def create(self, validated_data):
        raise NotImplementedError("Cannot create")

    def update(self, instance, validated_data):
        raise NotImplementedError("Cannot update")


class SubscriptionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Subscription
        fields = (
            "plan_id",
            "plan_name",
            "start_date",
            "end_date",
            "status_display",
            "is_active",
            "period_end",
            "period_start",
            "can_access_customer_portal",
        )
        read_only_fields = (
            "plan_id",
            "plan_name",
            "start_date",
            "end_date",
            "status_display",
            "is_active",
            "period_end",
            "period_start",
            "can_access_customer_portal",
        )

    plan_id = serializers.PrimaryKeyRelatedField(source="plan.id", read_only=True)
    plan_name = serializers.Char<PERSON>ield(source="plan.name", read_only=True)
    status_display = serializers.CharField(source="get_status_display", read_only=True)

    def create(self, validated_data):
        raise NotImplementedError("Cannot create")

    def update(self, instance, validated_data):
        raise NotImplementedError("Cannot update")
