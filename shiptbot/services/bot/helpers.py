from shiptbot.models.bot.constants import OFFER_STATUS
from shiptbot.models.bot.models import Offer
from shiptbot.services.mailgun.helpers import EmailHelper
from shiptbot.services.telegram.helpers import TelegramHelper


class NotificationHelper:
    def __init__(self, user):
        self.user = user
        self.setting = user.botsetting

    def send_notification(self, offer: Offer):
        if offer.status == OFFER_STATUS.ACCEPTED:
            self._send_offer_accepted_notis(offer)

        if offer.status == OFFER_STATUS.GONE:
            pass

    def _send_offer_accepted_notis(self, offer: Offer):
        if self.setting.notis_via_email:
            Email<PERSON>elper(self.user).send_offer_accepted_email(offer)

        if self.setting.notis_via_telegram:
            Telegram<PERSON>elper(self.user).send_offer_accepted_message(offer)
