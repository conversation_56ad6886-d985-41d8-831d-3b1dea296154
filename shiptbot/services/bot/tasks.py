import logging

import arrow

from shiptbot.models.bot.models import Day, Range, Session
from shiptbot.models.user.models import User
from shiptbot.services.bot.shipt_bot import ShiptBot
from shiptbot.taskapp import celery_app
from shiptbot.taskapp.base import LoggingTask

logger = logging.getLogger(__name__)


@celery_app.task(base=LoggingTask, bind=True, max_retries=0)
def async_start_bot(self, user_id, session_id):
    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        raise Exception("User does not exist")

    if not user.shipt_logged_in:
        raise Exception("User is not logged in")

    try:
        session = user.session_set.get(id=session_id)
    except Session.DoesNotExist:
        raise Exception("Session does not exist")

    shiptbot = ShiptBot(user, session)
    shiptbot.start_seaching()


@celery_app.task(base=LoggingTask, bind=True, max_retries=0)
def async_start_scheduled_search(self):
    now = arrow.utcnow()
    # ... existing code


@celery_app.task(base=LoggingTask, bind=True, max_retries=0)
def auto_start_bot_task(self):
    """Auto-start bots for users based on their scheduled time ranges"""
    from shiptbot.models.bot.constants import DAYS

    # Get all ranges with their related objects
    ranges = Range.objects.select_related("user", "day").filter(
        day__enabled=True, user__shipt_is_running=False
    )

    for range_obj in ranges:
        user = range_obj.user

        # Skip if user doesn't have timezone set
        if not user.timezone:
            continue

        try:
            # Convert current UTC time to user's timezone
            user_now = arrow.utcnow().to(user.timezone)
            user_today = user_now.format(
                "ddd"
            ).lower()  # Get day abbreviation (mon, tue, etc.)

            # Check if today matches the range's day
            if range_obj.day.day != user_today:
                continue

            # Check if current time falls within the range
            current_time = user_now.time()
            if not (range_obj.start_time <= current_time <= range_obj.end_time):
                continue

            # Check user conditions
            if not user.shipt_logged_in:
                continue

            subscription = user.subscription
            if not subscription.is_active:
                continue

            # All conditions met - start the bot
            user.shipt_is_running = True
            user.save()

            session = Session.objects.create(user=user)
            async_start_bot.delay(user.id, session.id)

        except Exception as e:
            logger.error(f"Error auto-starting bot for user {user.id}", exc_info=e)


@celery_app.task(base=LoggingTask, bind=True, max_retries=0)
def auto_stop_bot_task(self):
    """Auto-stop bots for users when outside their scheduled time ranges"""

    # Get all users with running bots
    running_users = User.objects.filter(shipt_is_running=True).prefetch_related(
        "day_set__range_set"
    )

    for user in running_users:
        # Skip if user doesn't have timezone set
        if not user.timezone:
            continue

        try:
            # Convert current UTC time to user's timezone
            user_now = arrow.utcnow().to(user.timezone)
            user_today = user_now.format("ddd").lower()  # Get day abbreviation
            current_time = user_now.time()

            # Check if user has any active ranges for current time
            has_active_range = False

            for day in user.day_set.filter(enabled=True, day=user_today):
                for range_obj in day.range_set.all():
                    if range_obj.start_time <= current_time <= range_obj.end_time:
                        has_active_range = True
                        break
                if has_active_range:
                    break

            # If no active range found, stop the bot
            if not has_active_range:
                user.shipt_is_running = False
                user.save()

                # Update the latest session's ended_at
                latest_session = user.get_latest_search_session()
                if latest_session and not latest_session.ended_at:
                    latest_session.ended_at = arrow.utcnow().datetime
                    latest_session.save()

        except Exception as e:
            logger.error(f"Error auto-stopping bot for user {user.id}", exc_info=e)
