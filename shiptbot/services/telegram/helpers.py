import logging
import re

import requests
from django.conf import settings

logger = logging.getLogger(__name__)


def send_telegram_message(chat_id, message):
    url = f"https://api.telegram.org/bot{settings.TELEGRAM_BOT_TOKEN}/sendMessage"
    data = {
        "chat_id": chat_id,
        "text": message,
        "parse_mode": "HTML",
    }

    response = requests.post(url, data=data)
    if response.ok:
        return response.json()

    logger.warning(
        f"Failed to send telegram message. Status code: {response.status_code}",
        extra=dict(response=response.text),
    )


def extract_email(text):
    match = re.search(r"[\w\.-]+@[\w\.-]+\.\w+", text)
    return match.group(0) if match else None


class TelegramHelper:
    def __init__(self, user):
        self.user = user
        self.chat_id = user.telegram_chat_id

    def _format_offer(self, offer):
        message = f"<b>Store</b>: {offer.store_name}\n"
        message += f"<b>Location</b>: {offer.store_location_name}\n"
        message += f"<b>Pay</b>: {offer.est_shopper_pay}\n"
        return message

    def send_offer_accepted_message(self, offer):
        message = (
            f"<b>OnlyShipster has grabbed you a new Offer!</b>\n"
            + self._format_offer(offer)
        )
        send_telegram_message(self.chat_id, message)
