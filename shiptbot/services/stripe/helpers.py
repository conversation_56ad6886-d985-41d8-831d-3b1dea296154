import logging

import arrow
import stripe
from django.conf import settings
from stripe.error import CardError

from shiptbot.models.subscription.models import Plan
from shiptbot.util.api_exceptions import APIBadRequestError

logger = logging.getLogger(__name__)

stripe.api_key = settings.STRIPE_SECRET_KEY


class StripeHelper:
    def __init__(self, user):
        self.user = user
        self.customer_id = self.user.stripe_customer_id
        if not self.customer_id:
            raise ValueError("User does not have a Stripe customer ID")

    @staticmethod
    def create_customer(user):
        customer = stripe.Customer.create(
            name=user.full_name,
            email=user.email,
        )
        user.stripe_customer_id = customer.id
        user.save()

        return customer

    def list_subscriptions(self):
        response = stripe.Subscription.list(
            limit=5,
            status="active",
            customer=self.customer_id,
        )
        return response.data

    def retrieve_subscription(self, subscription_id):
        return stripe.Subscription.retrieve(subscription_id)

    def retrieve_current_subscription(self):
        """Get the current subscription for the user from Stripe"""
        subscription = None

        subscriptions = self.list_subscriptions()
        if len(subscriptions) > 1:
            logger.warning(
                f"User {self.user.email} has more than one active subscription."
            )

        if len(subscriptions) > 0:
            subscription = subscriptions[0]

        if not subscription:
            stripe_subscription_id = self.user.subscription.stripe_subscription_id
            if not stripe_subscription_id:
                return None

            subscription = self.retrieve_subscription(
                self.user.subscription.stripe_subscription_id
            )

        return subscription

    def sync_subscription(self):
        """Sync the subscription from Stripe to our database"""
        stripe_sub = self.retrieve_current_subscription()
        if not stripe_sub:
            return None

        if stripe_sub.quantity > 1:
            logger.warning(
                f"User {self.user.email} has subscription quantity greater than 1."
            )

        start_date = arrow.get(stripe_sub.start_date).datetime
        end_date = None
        if stripe_sub.ended_at:
            end_date = arrow.get(stripe_sub.ended_at).datetime

        stripe_price_id = stripe_sub.plan.id
        try:
            plan = Plan.objects.get(stripe_price_id=stripe_price_id)
        except Plan.DoesNotExist:
            logger.warning(
                f"Plan with Stripe price ID {stripe_price_id} does not exist"
            )
            plan = None

        current_sub = self.user.subscription
        current_sub.stripe_upstream_data = stripe_sub
        current_sub.stripe_subscription_id = stripe_sub.id
        current_sub.start_date = start_date
        current_sub.end_date = end_date
        current_sub.plan = plan

        if current_sub.is_past_due:
            current_sub.stripe_upstream_data = self.cancel_subscription(
                current_sub.stripe_subscription_id
            )
            latest_invoice = current_sub.get_latest_invoice()
            if latest_invoice:
                stripe.Invoice.void_invoice(latest_invoice)

        current_sub.save()

        return current_sub

    def create_checkout_session(self, plan):
        session = stripe.checkout.Session.create(
            customer=self.customer_id,
            success_url=settings.STRIPE_CHECKOUT_SUCCESS_URL,
            line_items=[
                {
                    "price": plan.stripe_price_id,
                    "quantity": 1,
                    "adjustable_quantity": {
                        "enabled": False,
                    },
                }
            ],
            mode="subscription",
        )
        return session

    def create_billing_portal_session(self):
        session = stripe.billing_portal.Session.create(
            customer=self.user.stripe_customer_id,
            return_url=settings.STRIPE_BILLING_PORTAL_RETURN_URL,
        )
        return session

    def cancel_subscription(self, subscription_id):
        subscription = stripe.Subscription.cancel(subscription_id)
        return subscription
