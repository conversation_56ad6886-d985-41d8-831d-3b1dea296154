import logging

import pydash
from django.http import HttpResponse
from rest_framework import status
from rest_framework.permissions import AllowAny
from rest_framework.views import APIView

from shiptbot.models.user.models import User
from shiptbot.services.telegram.helpers import extract_email, send_telegram_message

logger = logging.getLogger(__name__)


class TelegramWebhookView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        data = request.data
        text = pydash.get(data, "message.text")
        chat_id = pydash.get(data, "message.chat.id")
        try:
            email = extract_email(text)
        except Exception as e:
            send_telegram_message(chat_id, "An error occurred. Please try again later.")
            logger.error(f"Error in telegram webhook extracting email", exc_info=e)

        try:
            if not email:
                send_telegram_message(
                    chat_id,
                    "Please provide a valid email address to start receiving notifications.",
                )
                return HttpResponse(status=status.HTTP_200_OK)

            try:
                user = User.objects.get(email=email)
                user.telegram_chat_id = chat_id
                user.save()
            except User.DoesNotExist:
                send_telegram_message(
                    chat_id, "No user found with this email. Please try again."
                )
                return HttpResponse(status=status.HTTP_200_OK)

            send_telegram_message(
                chat_id, "You will now receive notifications for your offers."
            )
        except Exception as e:
            send_telegram_message(chat_id, "An error occurred. Please try again later.")
            logger.error(f"Error in telegram webhook", exc_info=e)

        return HttpResponse(status=status.HTTP_200_OK)
